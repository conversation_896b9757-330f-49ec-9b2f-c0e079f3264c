<template>
  <div class="standalone-world-map" :style="containerStyle">
    <div class="map-title" v-if="showTitle">
      <span class="title-text">{{ title }}</span>
    </div>
    <div :id="mapId" class="map-container" :class="{ 'show-grid': showGrid }" :style="mapContainerStyle"></div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl'

export default {
  name: 'StandaloneWorldMap',
  props: {
    // Map configuration
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '500px'
    },
    title: {
      type: String,
      default: 'Global Distribution'
    },
    showTitle: {
      type: Boolean,
      default: true
    },

    // Map behavior
    center: {
      type: Array,
      default: () => [20, 0]
    },
    zoom: {
      type: Number,
      default: 2
    },
    // 区域模式配置 (Regional mode configuration)
    regionalMode: {
      type: <PERSON>olean,
      default: false
    },
    // 区域配置对象 (Regional configuration object)
    regionConfig: {
      type: Object,
      default: () => ({
        name: 'global',
        center: [20, 0],
        zoom: 2,
        bounds: null
      })
    },

    // Path data
    pathData: {
      type: Array,
      default: () => []
    },

    // Styling options
    colors: {
      type: Object,
      default: () => ({
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#1ED9B5',
        sourcePoint: '#1ED9B5',
        targetPoint: '#1ED9B5'
      })
    },

    // Animation settings
    animationEnabled: {
      type: Boolean,
      default: true
    },
    animationSpeed: {
      type: Number,
      default: 2000
    },
    flowAnimationSpeed: {
      type: Number,
      default: 0.003, // Slower speed for more deliberate animation
      validator: (value) => value > 0 && value <= 0.1
    },
    flowParticleCount: {
      type: Number,
      default: 1, // Sequential particles - one at a time
      validator: (value) => value >= 1 && value <= 10
    },

    // Grid overlay
    showGrid: {
      type: Boolean,
      default: true
    },

    // 最小化模式 - 隐藏文本标签和边界 (Minimalist mode - hide text labels and borders)
    minimalistMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      flowAnimations: [], // Store flow animation data
      mapboxLoaded: false,
      mapId: `world-map-${Math.random().toString(36).substr(2, 9)}`,
      // Mapbox GL JS 配置 (Mapbox GL JS configuration)
      mapboxAccessToken: 'pk.eyJ1IjoiYmFyNXhjIiwiYSI6ImNtYXdhdzcwZDBiajUydHIycjh0OXZrYW8ifQ.1pfjx8FkKHbR4n94jINJNw',
      mapboxStyleUrl: 'mapbox://styles/bar5xc/cmbou6n4400ny01s65spx6eky',
      // 动画和图层管理 (Animation and layer management)
      animationFrameIds: [],
      pathSources: new Map(),
      markerElements: new Map(),
      // 增强动画效果 (Enhanced animation effects)
      rippleElements: new Map(),
      pulseRings: new Map(),
      flowParticleColor: '#FF6B6B', // 对比色用于流动粒子 (Contrasting color for flow particles)
      particleDelayBetweenCycles: 2000 // 粒子循环间延迟 (Delay between particle cycles)
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    mapContainerStyle() {
      return {
        backgroundColor: this.colors.ocean
      }
    },
    // 计算实际使用的地图中心点 (Calculate actual map center to use)
    // Mapbox uses [longitude, latitude] format
    actualMapCenter() {
      const center = this.regionalMode && this.regionConfig.center
        ? this.regionConfig.center
        : this.center
      // Convert from [lat, lng] to [lng, lat] for Mapbox
      return [center[1], center[0]]
    },
    // 计算实际使用的缩放级别 (Calculate actual zoom level to use)
    actualMapZoom() {
      return this.regionalMode && this.regionConfig.zoom
        ? this.regionConfig.zoom
        : this.zoom
    }
  },
  mounted() {
    this.initMapbox()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    pathData: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          this.renderPaths()
        }
      },
      deep: true
    },
    animationEnabled: {
      handler(newValue) {
        if (!newValue) {
          // Stop all flow animations when animation is disabled
          this.flowAnimations.forEach(animationData => {
            if (animationData.animationFrameId) {
              cancelAnimationFrame(animationData.animationFrameId)
              animationData.animationFrameId = null
            }
            animationData.isActive = false
          })
        } else if (this.map && this.mapboxLoaded) {
          // Restart animations when re-enabled
          this.renderPaths()
        }
      }
    },
    // 监听区域模式变化 (Watch for regional mode changes)
    regionalMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          // 重新设置地图视图 (Reset map view)
          this.updateMapView()
        }
      }
    },

    // 监听简洁模式变化 (Watch for minimalist mode changes)
    minimalistMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          console.log('简洁模式切换 (Minimalist mode toggled):', this.minimalistMode)
          // 重新渲染路径 (Re-render paths)
          this.renderPaths()
        }
      }
    },
    // 监听区域配置变化 (Watch for region config changes)
    regionConfig: {
      handler() {
        if (this.map && this.mapboxLoaded && this.regionalMode) {
          // 更新地图视图 (Update map view)
          this.updateMapView()
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化Mapbox GL JS地图 (Initialize Mapbox GL JS map)
     */
    initMapbox() {
      console.log('开始初始化Mapbox地图 (Starting Mapbox map initialization)')
      console.log('访问令牌 (Access token):', this.mapboxAccessToken.substring(0, 20) + '...')
      console.log('样式URL (Style URL):', this.mapboxStyleUrl)
      console.log('地图中心点 (Map center):', this.actualMapCenter)
      console.log('缩放级别 (Zoom level):', this.actualMapZoom)

      // 设置Mapbox访问令牌 (Set Mapbox access token)
      mapboxgl.accessToken = this.mapboxAccessToken

      // 确保Mapbox CSS已加载 (Ensure Mapbox CSS is loaded)
      this.loadMapboxCSS()

      try {
        // 创建地图实例 - 让Mapbox样式处理默认视图设置 (Create map instance - let Mapbox style handle default view settings)
        this.map = new mapboxgl.Map({
          container: this.mapId,
          style: this.mapboxStyleUrl,
          // 不设置center和zoom，让样式配置处理 (Don't set center and zoom, let style configuration handle it)
          interactive: false, // 禁用交互以匹配原始行为 (Disable interaction to match original behavior)
          attributionControl: false,
          logoPosition: 'bottom-right'
        })
        console.log('Mapbox地图实例创建成功 (Mapbox map instance created successfully)')
      } catch (error) {
        console.error('创建Mapbox地图实例失败 (Failed to create Mapbox map instance):', error)
        return
      }

      // 地图加载完成后的处理 (Handle map load completion)
      this.map.on('load', () => {
        console.log('Mapbox地图加载完成 (Mapbox map loaded)')
        this.mapboxLoaded = true

        // 记录当前地图视图信息 (Log current map view info)
        console.log('地图当前中心点 (Current map center):', this.map.getCenter())
        console.log('地图当前缩放级别 (Current map zoom):', this.map.getZoom())

        // 只在明确需要时设置边界（区域模式且有边界配置）(Only set bounds when explicitly needed)
        if (this.regionalMode && this.regionConfig.bounds) {
          this.applyRegionalBounds()
        }

        // 渲染路径数据 (Render path data)
        if (this.pathData.length > 0) {
          this.renderPaths()
        }

        // 发出地图就绪事件 (Emit map ready event)
        this.$emit('map-ready', this.map)
      })

      // 错误处理 (Error handling)
      this.map.on('error', (e) => {
        console.error('Mapbox地图加载错误 (Mapbox map load error):', e)
        console.error('错误详情 (Error details):', {
          error: e.error,
          sourceId: e.sourceId,
          isSourceLoaded: e.isSourceLoaded
        })
      })

      // 样式加载事件 (Style load events)
      this.map.on('styledata', () => {
        console.log('Mapbox样式数据加载完成 (Mapbox style data loaded)')
      })

      this.map.on('sourcedataloading', (e) => {
        console.log('Mapbox数据源开始加载 (Mapbox source data loading):', e.sourceId)
      })

      this.map.on('sourcedata', (e) => {
        console.log('Mapbox数据源加载完成 (Mapbox source data loaded):', e.sourceId)
      })

      // 添加点击事件用于调试 (Add click event for debugging)
      this.map.on('click', (e) => {
        console.log('地图点击位置 (Map click position):', e.lngLat)
      })
    },

    /**
     * 加载Mapbox CSS样式 (Load Mapbox CSS styles)
     */
    loadMapboxCSS() {
      if (!document.querySelector('link[href*="mapbox-gl.css"]')) {
        const mapboxCss = document.createElement('link')
        mapboxCss.rel = 'stylesheet'
        mapboxCss.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
        document.head.appendChild(mapboxCss)
      }
    },

    /**
     * 渲染路径数据到Mapbox地图 (Render path data to Mapbox map)
     */
    renderPaths() {
      if (!this.map || !this.mapboxLoaded) return

      console.log('开始渲染路径 (Starting to render paths):', this.pathData.length)

      // 清除现有的路径和动画 (Clear existing paths and animations)
      this.clearMapLayers()

      // 为每条路径创建数据源和图层 (Create data sources and layers for each path)
      this.pathData.forEach((path, index) => {
        this.renderSinglePath(path, index)
      })
    },

    /**
     * 渲染单条路径到Mapbox地图 (Render single path to Mapbox map)
     */
    renderSinglePath(path, index) {
      if (!path.coords || path.coords.length < 2) {
        console.warn(`路径 ${index} 坐标无效 (Path ${index} has invalid coordinates):`, path)
        return
      }

      const [sourceCoords, targetCoords] = path.coords
      const pathId = `path-${index}`
      const sourceId = `source-${index}`
      const targetId = `target-${index}`

      console.log(`渲染路径 ${index} (Rendering path ${index}):`, {
        name: path.name,
        sourceCoords,
        targetCoords,
        pathId
      })

      // 生成曲线路径点 (Generate curved path points)
      const curvePoints = this.generateCurvePoints(sourceCoords, targetCoords)

      // 转换坐标格式为Mapbox格式 [lng, lat] (Convert coordinates to Mapbox format [lng, lat])
      const mapboxCurvePoints = curvePoints.map(point => [point[1], point[0]])

      // 🔍 关键调试：验证坐标对齐 (Critical debugging: verify coordinate alignment)
      console.log(`🔍 坐标对齐调试 (Coordinate alignment debugging) - Path ${index}:`, {
        originalSourceCoords: sourceCoords,
        originalTargetCoords: targetCoords,
        markerSourceMapboxCoords: [sourceCoords[1], sourceCoords[0]], // 标记使用的坐标 (Marker coordinates)
        markerTargetMapboxCoords: [targetCoords[1], targetCoords[0]], // 标记使用的坐标 (Marker coordinates)
        curveStartPoint: curvePoints[0],
        curveEndPoint: curvePoints[curvePoints.length - 1],
        mapboxCurveStartPoint: mapboxCurvePoints[0],
        mapboxCurveEndPoint: mapboxCurvePoints[mapboxCurvePoints.length - 1],
        startPointAlignment: {
          markerCoords: [sourceCoords[1], sourceCoords[0]],
          curveCoords: mapboxCurvePoints[0],
          difference: [
            Math.abs(sourceCoords[1] - mapboxCurvePoints[0][0]),
            Math.abs(sourceCoords[0] - mapboxCurvePoints[0][1])
          ]
        },
        endPointAlignment: {
          markerCoords: [targetCoords[1], targetCoords[0]],
          curveCoords: mapboxCurvePoints[mapboxCurvePoints.length - 1],
          difference: [
            Math.abs(targetCoords[1] - mapboxCurvePoints[mapboxCurvePoints.length - 1][0]),
            Math.abs(targetCoords[0] - mapboxCurvePoints[mapboxCurvePoints.length - 1][1])
          ]
        }
      })

      console.log(`路径 ${index} 曲线点数量 (Path ${index} curve points count):`, mapboxCurvePoints.length)

      // 创建路径线的GeoJSON数据 (Create GeoJSON data for path line)
      const pathGeoJSON = {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: mapboxCurvePoints
        },
        properties: {
          name: path.name || `Path ${index}`,
          value: path.value || 50
        }
      }

      try {
        // 添加路径数据源 (Add path data source)
        if (!this.map.getSource(pathId)) {
          this.map.addSource(pathId, {
            type: 'geojson',
            data: pathGeoJSON
          })
          console.log(`路径数据源已添加 (Path data source added): ${pathId}`)
        }

        // 添加路径线图层 (Add path line layer)
        if (!this.map.getLayer(`${pathId}-line`)) {
          this.map.addLayer({
            id: `${pathId}-line`,
            type: 'line',
            source: pathId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': this.colors.pathLine,
              'line-width': 2,
              'line-opacity': 0.8
            }
          })
          console.log(`路径线图层已添加 (Path line layer added): ${pathId}-line`)

          // 添加发光效果 (Add glow effect)
          this.map.addLayer({
            id: `${pathId}-glow`,
            type: 'line',
            source: pathId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': this.colors.pathLine,
              'line-width': 6,
              'line-opacity': 0.3,
              'line-blur': 2
            }
          }, `${pathId}-line`) // 将发光层放在线条层下方 (Place glow layer below line layer)
          console.log(`路径发光图层已添加 (Path glow layer added): ${pathId}-glow`)
        }
      } catch (error) {
        console.error(`添加路径图层失败 (Failed to add path layers) ${pathId}:`, error)
        return
      }

      // 创建起点和终点标记 (Create source and target markers)
      this.createMarker(sourceCoords, sourceId, 'source', path, index)
      this.createMarker(targetCoords, targetId, 'target', path, index)

      // 存储路径信息 (Store path information)
      this.pathSources.set(pathId, {
        sourceId: pathId,
        layerIds: [`${pathId}-line`, `${pathId}-glow`],
        curvePoints: curvePoints,
        pathData: path
      })

      // 如果启用动画，创建流动动画 (If animation enabled, create flow animation)
      // 注意：传递mapboxCurvePoints以确保坐标系统一致 (Note: pass mapboxCurvePoints to ensure coordinate system consistency)
      if (this.animationEnabled) {
        this.createFlowAnimation(mapboxCurvePoints, index, pathId)
      }
    },

    /**
     * 创建标记点 (Create marker)
     */
    createMarker(coords, markerId, type, pathData, index) {
      // 创建标记容器 (Create marker container)
      const markerContainer = document.createElement('div')
      markerContainer.className = `marker-container ${type}-marker-container`
      markerContainer.style.cssText = `
        position: relative;
        width: ${Math.max(16, (pathData.value || 50) / 25)}px;
        height: ${Math.max(16, (pathData.value || 50) / 25)}px;
      `

      // 创建主标记元素 (Create main marker element)
      const markerElement = document.createElement('div')
      markerElement.className = `marker ${type}-marker`
      markerElement.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: ${Math.max(8, (pathData.value || 50) / 30)}px;
        height: ${Math.max(8, (pathData.value || 50) / 30)}px;
        background-color: ${this.colors[type + 'Point']};
        border: 2px solid ${this.colors[type + 'Point']};
        border-radius: 50%;
        box-shadow: 0 0 10px ${this.colors[type + 'Point']};
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 3;
      `

      // 创建脉冲外环 (Create pulsing outer ring)
      const pulseRing = document.createElement('div')
      pulseRing.className = `pulse-ring ${type}-pulse-ring`
      pulseRing.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: ${Math.max(20, (pathData.value || 50) / 20)}px;
        height: ${Math.max(20, (pathData.value || 50) / 20)}px;
        border: 2px solid ${this.colors[type + 'Point']};
        border-radius: 50%;
        background: transparent;
        opacity: 0;
        z-index: 1;
      `

      // 创建涟漪效果容器 (Create ripple effect container)
      const rippleContainer = document.createElement('div')
      rippleContainer.className = `ripple-container ${type}-ripple-container`
      rippleContainer.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        pointer-events: none;
        z-index: 2;
      `

      // 组装标记 (Assemble marker)
      markerContainer.appendChild(pulseRing)
      markerContainer.appendChild(rippleContainer)
      markerContainer.appendChild(markerElement)

      // 创建Mapbox标记 (Create Mapbox marker)
      const marker = new mapboxgl.Marker(markerContainer)
        .setLngLat([coords[1], coords[0]]) // Mapbox uses [lng, lat]
        .addTo(this.map)

      // 添加点击事件和提示信息 (Add click event and tooltip)
      if (pathData.name) {
        const tooltipText = type === 'source'
          ? pathData.name.split(' to ')[0] || 'Source'
          : pathData.name.split(' to ')[1] || 'Target'

        markerElement.title = tooltipText
      }

      // 存储标记引用 (Store marker reference)
      this.markerElements.set(markerId, {
        marker: marker,
        element: markerElement,
        container: markerContainer,
        pulseRing: pulseRing,
        rippleContainer: rippleContainer,
        type: type,
        coords: coords,
        pathData: pathData
      })

      // 存储脉冲环引用 (Store pulse ring reference)
      this.pulseRings.set(markerId, pulseRing)

      // 存储涟漪容器引用 (Store ripple container reference)
      this.rippleElements.set(markerId, rippleContainer)

      // 如果启用动画，开始标记动画 (If animation enabled, start marker animation)
      if (this.animationEnabled) {
        this.animateMarker(markerElement, index + (type === 'target' ? 0.5 : 0))
        this.animatePulseRing(pulseRing, index + (type === 'target' ? 0.3 : 0))
      }

      return marker
    },

    /**
     * 生成曲线路径点 (Generate curved path points)
     */
    generateCurvePoints(start, end, numPoints = 30) {
      const points = []

      console.log(`生成曲线路径 (Generating curve path):`, {
        start: start,
        end: end,
        numPoints: numPoints
      })

      // 确保起点和终点坐标格式正确 [lat, lng] (Ensure start and end coordinates are in correct [lat, lng] format)
      const startLat = start[0]
      const startLng = start[1]
      const endLat = end[0]
      const endLng = end[1]

      // Calculate the midpoint
      const midLat = (startLat + endLat) / 2
      const midLng = (startLng + endLng) / 2

      // Calculate the distance for curve height
      const distance = Math.sqrt(
        Math.pow(endLat - startLat, 2) + Math.pow(endLng - startLng, 2)
      )

      // Add curvature based on distance - 增加曲率以获得更明显的弧形 (Increase curvature for more pronounced arc)
      const curvature = distance * 0.4

      // Determine curve direction - 根据地理位置调整曲线方向 (Adjust curve direction based on geographic position)
      const curveDirection = midLat > 0 ? 1 : -1
      const controlPoint = [midLat + (curvature * curveDirection), midLng]

      console.log(`曲线控制点 (Curve control point):`, controlPoint)

      // Generate points along the quadratic bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints
        const lat = Math.pow(1 - t, 2) * startLat + 2 * (1 - t) * t * controlPoint[0] + Math.pow(t, 2) * endLat
        const lng = Math.pow(1 - t, 2) * startLng + 2 * (1 - t) * t * controlPoint[1] + Math.pow(t, 2) * endLng
        points.push([lat, lng])
      }

      console.log(`生成的曲线点数量 (Generated curve points count):`, points.length)
      console.log(`起点 (Start point):`, points[0])
      console.log(`终点 (End point):`, points[points.length - 1])

      return points
    },

    /**
     * 动画标记点 (Animate marker)
     */
    animateMarker(markerElement, index) {
      if (!markerElement || !this.animationEnabled) return

      let opacity = 0.3
      let increasing = true

      const animate = () => {
        if (increasing) {
          opacity += 0.015
          if (opacity >= 1) {
            increasing = false
          }
        } else {
          opacity -= 0.015
          if (opacity <= 0.3) {
            increasing = true
          }
        }

        // 更新标记元素的透明度和发光效果 (Update marker element opacity and glow effect)
        markerElement.style.opacity = opacity
        markerElement.style.boxShadow = `0 0 ${10 + opacity * 10}px ${markerElement.style.backgroundColor}`

        // 继续动画如果标记仍然有效且动画已启用 (Continue animation if marker is still valid and animation is enabled)
        if (markerElement.parentNode && this.animationEnabled) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }

      // 根据索引延迟开始动画 (Start animation with delay based on index)
      setTimeout(() => {
        if (this.animationEnabled && markerElement.parentNode) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }, index * 150)
    },

    /**
     * 动画脉冲外环 (Animate pulse ring)
     */
    animatePulseRing(pulseRing, index) {
      if (!pulseRing || !this.animationEnabled) return

      let scale = 0.8
      let opacity = 0
      let increasing = true

      const animate = () => {
        if (increasing) {
          scale += 0.02
          opacity += 0.03
          if (scale >= 1.5) {
            increasing = false
          }
        } else {
          scale -= 0.02
          opacity -= 0.03
          if (scale <= 0.8) {
            increasing = true
          }
        }

        // 更新脉冲环的缩放和透明度 (Update pulse ring scale and opacity)
        pulseRing.style.transform = `translate(-50%, -50%) scale(${scale})`
        pulseRing.style.opacity = Math.max(0, Math.min(0.6, opacity))

        // 继续动画如果元素仍然有效且动画已启用 (Continue animation if element is still valid and animation is enabled)
        if (pulseRing.parentNode && this.animationEnabled) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }

      // 根据索引延迟开始动画 (Start animation with delay based on index)
      setTimeout(() => {
        if (this.animationEnabled && pulseRing.parentNode) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }, index * 200)
    },

    /**
     * 创建流动动画 (Create flow animation)
     * @param {Array} mapboxCurvePoints - 曲线点数组，格式为[lng, lat] (Curve points array in [lng, lat] format)
     */
    createFlowAnimation(mapboxCurvePoints, pathIndex, pathId) {
      if (!this.map || !this.animationEnabled || mapboxCurvePoints.length < 2) {
        console.warn(`流动动画创建失败 (Flow animation creation failed):`, {
          hasMap: !!this.map,
          animationEnabled: this.animationEnabled,
          pointsCount: mapboxCurvePoints?.length || 0
        })
        return
      }

      // 验证路径端点与标记位置的对齐 (Verify path endpoint alignment with marker positions)
      const sourceMarkerInfo = this.markerElements.get(`source-${pathIndex}`)
      const targetMarkerInfo = this.markerElements.get(`target-${pathIndex}`)

      if (sourceMarkerInfo && targetMarkerInfo) {
        const sourceMarkerCoords = sourceMarkerInfo.marker.getLngLat()
        const targetMarkerCoords = targetMarkerInfo.marker.getLngLat()

        console.log(`🔍 路径端点对齐验证 (Path endpoint alignment verification) - Path ${pathIndex}:`, {
          curveStartPoint: mapboxCurvePoints[0],
          curveEndPoint: mapboxCurvePoints[mapboxCurvePoints.length - 1],
          sourceMarkerCoords: [sourceMarkerCoords.lng, sourceMarkerCoords.lat],
          targetMarkerCoords: [targetMarkerCoords.lng, targetMarkerCoords.lat],
          startPointDiff: [
            Math.abs(mapboxCurvePoints[0][0] - sourceMarkerCoords.lng),
            Math.abs(mapboxCurvePoints[0][1] - sourceMarkerCoords.lat)
          ],
          endPointDiff: [
            Math.abs(mapboxCurvePoints[mapboxCurvePoints.length - 1][0] - targetMarkerCoords.lng),
            Math.abs(mapboxCurvePoints[mapboxCurvePoints.length - 1][1] - targetMarkerCoords.lat)
          ]
        })
      }

      console.log(`🚀 创建流动动画 (Creating flow animation) for path ${pathIndex}:`, {
        pointsCount: mapboxCurvePoints.length,
        startPoint: mapboxCurvePoints[0],
        endPoint: mapboxCurvePoints[mapboxCurvePoints.length - 1],
        flowAnimationSpeed: this.flowAnimationSpeed,
        particleDelayBetweenCycles: this.particleDelayBetweenCycles
      })

      // 获取起点和终点标记ID (Get source and target marker IDs)
      const sourceMarkerId = `source-${pathIndex}`
      const targetMarkerId = `target-${pathIndex}`

      // 创建单个流动粒子用于顺序动画 (Create single flow particle for sequential animation)
      const particleContainer = document.createElement('div')
      particleContainer.className = 'flow-particle-container'
      particleContainer.style.cssText = `
        position: relative;
        width: 12px;
        height: 12px;
        pointer-events: none;
        z-index: 1000;
      `

      // 创建箭头形状的粒子 (Create arrow-shaped particle)
      const particleElement = document.createElement('div')
      particleElement.className = 'flow-particle'
      particleElement.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 8px solid ${this.flowParticleColor};
        filter: drop-shadow(0 0 4px ${this.flowParticleColor});
        transition: transform 0.1s ease;
      `

      particleContainer.appendChild(particleElement)

      // 创建Mapbox标记 (Create Mapbox marker)
      // mapboxCurvePoints已经是[lng, lat]格式，直接使用 (mapboxCurvePoints is already in [lng, lat] format, use directly)
      const particleMarker = new mapboxgl.Marker(particleContainer)
        .setLngLat(mapboxCurvePoints[0]) // 从起点开始 (Start from beginning)
        .addTo(this.map)

      // 动画状态 (Animation state)
      const animationData = {
        particle: {
          marker: particleMarker,
          element: particleElement,
          container: particleContainer,
          progress: 0,
          pathIndex: pathIndex,
          isVisible: false
        },
        mapboxCurvePoints: mapboxCurvePoints, // 使用Mapbox格式的坐标点 (Use Mapbox format coordinate points)
        pathIndex: pathIndex,
        pathId: pathId,
        sourceMarkerId: sourceMarkerId,
        targetMarkerId: targetMarkerId,
        animationFrameId: null,
        isActive: true,
        cycleStartTime: 0
      }

      this.flowAnimations.push(animationData)

      // 动画函数 (Animation function)
      const animateFlow = () => {
        if (!this.animationEnabled || !animationData.isActive) {
          return
        }

        const particle = animationData.particle
        const currentTime = Date.now()

        // 检查是否应该开始新的循环 (Check if should start new cycle)
        if (!particle.isVisible && (currentTime - animationData.cycleStartTime) >= this.particleDelayBetweenCycles) {
          particle.isVisible = true
          particle.progress = 0
          particle.container.style.opacity = '1'

          // 触发起点涟漪效果 (Trigger source ripple effect)
          this.createRippleEffect(sourceMarkerId, 'departure')

          animationData.cycleStartTime = currentTime
        }

        if (particle.isVisible) {
          // 更新粒子进度 (Update particle progress)
          particle.progress += this.flowAnimationSpeed

          // 当粒子到达终点时 (When particle reaches the end)
          if (particle.progress >= 1) {
            particle.progress = 1
            particle.isVisible = false
            particle.container.style.opacity = '0'

            console.log(`✅ 粒子到达终点 (Particle reached end) - Path ${pathIndex}`)

            // 触发终点涟漪效果 (Trigger target ripple effect)
            this.createRippleEffect(targetMarkerId, 'arrival')

            // 重置循环计时器 (Reset cycle timer)
            animationData.cycleStartTime = currentTime
          } else {
            // 计算沿曲线的位置和方向 (Calculate position and direction along the curve)
            // 使用Mapbox格式的坐标点 (Use Mapbox format coordinate points)
            const position = this.getPositionAlongMapboxPath(animationData.mapboxCurvePoints, particle.progress)
            const direction = this.getDirectionAlongMapboxPath(animationData.mapboxCurvePoints, particle.progress)

            if (position && direction) {
              try {
                // 调试信息：仅在关键点记录 (Debug info: log only at key points)
                if (particle.progress === 0 || particle.progress >= 0.5 && particle.progress < 0.51) {
                  const correctedAngle = Math.atan2(direction.x, -direction.y) * 180 / Math.PI
                  console.log(`🔄 粒子关键位置 (Particle key position) - Path ${pathIndex}:`, {
                    progress: particle.progress.toFixed(3),
                    position: position,
                    direction: direction,
                    oldAngleCalculation: Math.atan2(direction.y, direction.x) * 180 / Math.PI,
                    correctedAngle: correctedAngle,
                    directionDescription: this.getDirectionDescription(correctedAngle)
                  })
                }

                // 更新粒子位置 (Update particle position)
                // position已经是[lng, lat]格式，直接使用 (position is already in [lng, lat] format, use directly)
                particle.marker.setLngLat(position)

                // 更新箭头方向 (Update arrow direction)
                // 修正箭头角度计算 - CSS箭头默认指向上方，需要调整到正确方向 (Fix arrow angle calculation - CSS arrow points up by default)
                // 我们的箭头使用 border-bottom 创建，默认指向上方(北)，所以需要旋转到方向向量的角度
                const angle = Math.atan2(direction.x, -direction.y) * 180 / Math.PI
                particle.element.style.transform = `translate(-50%, -50%) rotate(${angle}deg)`

                // 根据进度添加淡入淡出效果 (Add fade effect based on progress)
                const opacity = Math.sin(particle.progress * Math.PI) * 0.8 + 0.2
                particle.container.style.opacity = opacity
              } catch (e) {
                // 优雅地处理潜在错误 (Handle potential errors gracefully)
                console.error(`❌ 流动动画错误 (Flow animation error) - Path ${pathIndex}:`, e)
                console.error('错误详情 (Error details):', {
                  position: position,
                  direction: direction,
                  progress: particle.progress,
                  mapboxCurvePointsLength: animationData.mapboxCurvePoints?.length
                })
                animationData.isActive = false
              }
            } else {
              console.warn(`⚠️ 位置或方向计算失败 (Position or direction calculation failed) - Path ${pathIndex}:`, {
                position: position,
                direction: direction,
                progress: particle.progress,
                pointsCount: animationData.mapboxCurvePoints?.length
              })
            }
          }
        }

        // 继续动画如果动画仍然活跃 (Continue animation if still active)
        if (animationData.isActive && this.animationEnabled) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        } else {
          // 如果动画应该停止，则清理 (Clean up if animation should stop)
          this.cleanupFlowAnimation(animationData)
        }
      }

      // 根据路径索引延迟开始动画 (Start the animation with a delay based on path index)
      setTimeout(() => {
        if (this.animationEnabled && animationData.isActive) {
          animationData.cycleStartTime = Date.now()
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        }
      }, pathIndex * 500)
    },

    /**
     * 获取沿路径的位置 (Get position along path)
     */
    getPositionAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算沿路径的确切位置 (Calculate the exact position along the path)
      const totalSegments = points.length - 1
      const segmentProgress = progress * totalSegments
      const segmentIndex = Math.floor(segmentProgress)
      const localProgress = segmentProgress - segmentIndex

      // 处理进度恰好为1的边缘情况 (Handle edge case where progress is exactly 1)
      if (segmentIndex >= totalSegments) {
        return points[points.length - 1]
      }

      // 在两点之间插值 (Interpolate between two points)
      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1]

      const lat = startPoint[0] + (endPoint[0] - startPoint[0]) * localProgress
      const lng = startPoint[1] + (endPoint[1] - startPoint[1]) * localProgress

      return [lat, lng]
    },

    /**
     * 获取沿路径的方向 (Get direction along path)
     * @param {Array} points - 路径点数组，格式为[lat, lng] (Path points array in [lat, lng] format)
     */
    getDirectionAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算当前段 (Calculate current segment)
      const totalSegments = points.length - 1
      const segmentProgress = progress * totalSegments
      let segmentIndex = Math.floor(segmentProgress)

      // 处理边缘情况 (Handle edge cases)
      if (segmentIndex >= totalSegments) {
        segmentIndex = totalSegments - 1
      }

      // 获取当前段的起点和终点 (Get start and end points of current segment)
      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1] || points[segmentIndex]

      // 计算方向向量 (Calculate direction vector)
      const dx = endPoint[1] - startPoint[1] // longitude difference
      const dy = endPoint[0] - startPoint[0] // latitude difference

      // 归一化方向向量 (Normalize direction vector)
      const length = Math.sqrt(dx * dx + dy * dy)
      if (length === 0) {
        return { x: 0, y: 1 } // Default direction
      }

      return {
        x: dx / length,
        y: dy / length
      }
    },

    /**
     * 获取沿Mapbox路径的位置 (Get position along Mapbox path)
     * @param {Array} mapboxPoints - Mapbox路径点数组，格式为[lng, lat] (Mapbox path points array in [lng, lat] format)
     */
    getPositionAlongMapboxPath(mapboxPoints, progress) {
      if (!mapboxPoints || mapboxPoints.length < 2 || progress < 0 || progress > 1) {
        console.warn('getPositionAlongMapboxPath 参数无效 (Invalid parameters):', {
          hasPoints: !!mapboxPoints,
          pointsLength: mapboxPoints?.length,
          progress: progress
        })
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算沿路径的确切位置 (Calculate the exact position along the path)
      const totalSegments = mapboxPoints.length - 1
      const segmentProgress = progress * totalSegments
      const segmentIndex = Math.floor(segmentProgress)
      const localProgress = segmentProgress - segmentIndex

      // 处理进度恰好为1的边缘情况 (Handle edge case where progress is exactly 1)
      if (segmentIndex >= totalSegments) {
        const finalPosition = mapboxPoints[mapboxPoints.length - 1]
        console.log('返回最终位置 (Returning final position):', finalPosition)
        return finalPosition
      }

      // 在两点之间插值 (Interpolate between two points)
      const startPoint = mapboxPoints[segmentIndex]
      const endPoint = mapboxPoints[segmentIndex + 1]

      if (!startPoint || !endPoint) {
        console.error('路径点缺失 (Missing path points):', {
          segmentIndex: segmentIndex,
          startPoint: startPoint,
          endPoint: endPoint,
          totalPoints: mapboxPoints.length
        })
        return null
      }

      const lng = startPoint[0] + (endPoint[0] - startPoint[0]) * localProgress
      const lat = startPoint[1] + (endPoint[1] - startPoint[1]) * localProgress

      return [lng, lat]
    },

    /**
     * 获取沿Mapbox路径的方向 (Get direction along Mapbox path)
     * @param {Array} mapboxPoints - Mapbox路径点数组，格式为[lng, lat] (Mapbox path points array in [lng, lat] format)
     */
    getDirectionAlongMapboxPath(mapboxPoints, progress) {
      if (!mapboxPoints || mapboxPoints.length < 2 || progress < 0 || progress > 1) {
        console.warn('getDirectionAlongMapboxPath 参数无效 (Invalid parameters):', {
          hasPoints: !!mapboxPoints,
          pointsLength: mapboxPoints?.length,
          progress: progress
        })
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算当前段 (Calculate current segment)
      const totalSegments = mapboxPoints.length - 1
      const segmentProgress = progress * totalSegments
      let segmentIndex = Math.floor(segmentProgress)

      // 处理边缘情况 (Handle edge cases)
      if (segmentIndex >= totalSegments) {
        segmentIndex = totalSegments - 1
      }

      // 获取当前段的起点和终点 (Get start and end points of current segment)
      const startPoint = mapboxPoints[segmentIndex]
      const endPoint = mapboxPoints[segmentIndex + 1] || mapboxPoints[segmentIndex]

      if (!startPoint || !endPoint) {
        console.error('方向计算中路径点缺失 (Missing path points in direction calculation):', {
          segmentIndex: segmentIndex,
          startPoint: startPoint,
          endPoint: endPoint,
          totalPoints: mapboxPoints.length
        })
        return { x: 1, y: 0 } // Default direction pointing east
      }

      // 计算方向向量 (Calculate direction vector)
      // 对于Mapbox格式[lng, lat]，x是经度差，y是纬度差 (For Mapbox format [lng, lat], x is longitude diff, y is latitude diff)
      const dx = endPoint[0] - startPoint[0] // longitude difference
      const dy = endPoint[1] - startPoint[1] // latitude difference

      // 归一化方向向量 (Normalize direction vector)
      const length = Math.sqrt(dx * dx + dy * dy)
      if (length === 0) {
        console.warn('方向向量长度为0 (Direction vector length is 0):', {
          startPoint: startPoint,
          endPoint: endPoint,
          dx: dx,
          dy: dy
        })
        return { x: 1, y: 0 } // Default direction pointing east
      }

      const direction = {
        x: dx / length,
        y: dy / length
      }

      // 调试：仅在起点和中点记录方向计算结果 (Debug: log direction calculation only at start and midpoint)
      if (progress === 0 || (progress >= 0.49 && progress <= 0.51)) {
        const correctedAngle = Math.atan2(direction.x, -direction.y) * 180 / Math.PI
        console.log('🧭 方向计算 (Direction calculation):', {
          progress: progress.toFixed(3),
          segmentIndex: segmentIndex,
          startPoint: startPoint,
          endPoint: endPoint,
          dx: dx.toFixed(6),
          dy: dy.toFixed(6),
          length: length.toFixed(6),
          direction: direction,
          oldAngle: Math.atan2(direction.y, direction.x) * 180 / Math.PI,
          correctedAngle: correctedAngle,
          directionDescription: this.getDirectionDescription(correctedAngle)
        })
      }

      return direction
    },

    /**
     * 获取方向描述 (Get direction description)
     * @param {number} angle - 角度（度） (Angle in degrees)
     */
    getDirectionDescription(angle) {
      // 标准化角度到 0-360 范围 (Normalize angle to 0-360 range)
      angle = ((angle % 360) + 360) % 360

      if (angle >= 337.5 || angle < 22.5) return '北 (North)'
      if (angle >= 22.5 && angle < 67.5) return '东北 (Northeast)'
      if (angle >= 67.5 && angle < 112.5) return '东 (East)'
      if (angle >= 112.5 && angle < 157.5) return '东南 (Southeast)'
      if (angle >= 157.5 && angle < 202.5) return '南 (South)'
      if (angle >= 202.5 && angle < 247.5) return '西南 (Southwest)'
      if (angle >= 247.5 && angle < 292.5) return '西 (West)'
      if (angle >= 292.5 && angle < 337.5) return '西北 (Northwest)'
      return '未知 (Unknown)'
    },

    /**
     * 创建涟漪效果 (Create ripple effect)
     */
    createRippleEffect(markerId, type) {
      const markerInfo = this.markerElements.get(markerId)
      if (!markerInfo || !markerInfo.rippleContainer) return

      // 创建涟漪元素 (Create ripple element)
      const ripple = document.createElement('div')
      ripple.className = `ripple ${type}-ripple`
      ripple.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 10px;
        height: 10px;
        border: 2px solid ${type === 'departure' ? this.colors.sourcePoint : this.colors.targetPoint};
        border-radius: 50%;
        background: transparent;
        opacity: 0.8;
        pointer-events: none;
      `

      markerInfo.rippleContainer.appendChild(ripple)

      // 动画涟漪扩散 (Animate ripple expansion)
      let scale = 0.5
      let opacity = 0.8

      const animateRipple = () => {
        scale += 0.1
        opacity -= 0.05

        ripple.style.transform = `translate(-50%, -50%) scale(${scale})`
        ripple.style.opacity = Math.max(0, opacity)

        if (opacity > 0 && scale < 8) {
          requestAnimationFrame(animateRipple)
        } else {
          // 移除涟漪元素 (Remove ripple element)
          if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple)
          }
        }
      }

      requestAnimationFrame(animateRipple)
    },

    /**
     * 清理流动动画 (Cleanup flow animation)
     */
    cleanupFlowAnimation(animationData) {
      if (!animationData) return

      // 取消动画帧 (Cancel animation frame)
      if (animationData.animationFrameId) {
        cancelAnimationFrame(animationData.animationFrameId)
        animationData.animationFrameId = null
      }

      // 从地图中移除粒子 (Remove particle from map)
      if (animationData.particle && animationData.particle.marker && this.map) {
        try {
          animationData.particle.marker.remove()
        } catch (e) {
          // 在清理过程中忽略错误 (Ignore errors during cleanup)
        }
      }

      // 标记为非活动状态 (Mark as inactive)
      animationData.isActive = false
    },

    /**
     * 清理地图图层 (Clear map layers)
     */
    clearMapLayers() {
      // 清理所有动画帧 (Clear all animation frames)
      this.animationFrameIds.forEach(id => {
        cancelAnimationFrame(id)
      })
      this.animationFrameIds = []

      // 清理路径数据源和图层 (Clear path data sources and layers)
      this.pathSources.forEach((pathInfo, pathId) => {
        // 移除图层 (Remove layers)
        pathInfo.layerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            this.map.removeLayer(layerId)
          }
        })

        // 移除数据源 (Remove data source)
        if (this.map.getSource(pathId)) {
          this.map.removeSource(pathId)
        }
      })
      this.pathSources.clear()

      // 清理标记 (Clear markers)
      this.markerElements.forEach((markerInfo) => {
        if (markerInfo.marker) {
          markerInfo.marker.remove()
        }
      })
      this.markerElements.clear()

      // 清理脉冲环 (Clear pulse rings)
      this.pulseRings.clear()

      // 清理涟漪元素 (Clear ripple elements)
      this.rippleElements.clear()

      // 清理流动动画 (Clear flow animations)
      this.flowAnimations.forEach(animationData => {
        this.cleanupFlowAnimation(animationData)
      })
      this.flowAnimations = []

      // 清理旧的数组（向后兼容）(Clear old arrays for backward compatibility)
      this.pathLines = []
      this.sourceMarkers = []
      this.targetMarkers = []
    },

    /**
     * 组件清理 (Component cleanup)
     */
    cleanup() {
      // 清理所有图层 (Clear all layers)
      this.clearMapLayers()

      // 移除地图 (Remove map)
      if (this.map) {
        this.map.remove()
        this.map = null
      }

      // 重置状态 (Reset state)
      this.mapboxLoaded = false
    },

    /**
     * 应用区域边界限制 (Apply regional bounds restrictions)
     */
    applyRegionalBounds() {
      if (!this.map || !this.mapboxLoaded || !this.regionConfig.bounds) return

      try {
        const bounds = this.regionConfig.bounds
        const mapboxBounds = [
          [bounds.west, bounds.south], // 西南角 (Southwest corner)
          [bounds.east, bounds.north]  // 东北角 (Northeast corner)
        ]
        this.map.setMaxBounds(mapboxBounds)
        console.log('区域边界已应用 (Regional bounds applied):', bounds)
      } catch (error) {
        console.error('应用区域边界失败 (Failed to apply regional bounds):', error)
      }
    },

    /**
     * 更新地图视图 - 仅在模式切换时使用 (Update map view - only used when switching modes)
     */
    updateMapView() {
      if (!this.map || !this.mapboxLoaded) return

      try {
        // 只在明确切换模式时才改变视图 (Only change view when explicitly switching modes)
        if (this.regionalMode && this.regionConfig.center && this.regionConfig.zoom) {
          // 切换到区域模式 (Switch to regional mode)
          this.map.flyTo({
            center: this.actualMapCenter,
            zoom: this.actualMapZoom,
            duration: 1000 // 1秒动画过渡 (1 second animation transition)
          })

          // 应用区域边界 (Apply regional bounds)
          this.applyRegionalBounds()

          console.log(`切换到区域模式 (Switched to regional mode): 中心点(Center) ${this.actualMapCenter}, 缩放级别(Zoom) ${this.actualMapZoom}`)
        } else if (!this.regionalMode) {
          // 切换到全球模式 - 清除边界限制 (Switch to global mode - clear boundary restrictions)
          this.map.setMaxBounds(null)

          // 可以选择飞回到全球视图或保持当前视图 (Can choose to fly back to global view or keep current view)
          // 这里我们保持当前视图，让用户手动调整 (Here we keep current view, let user adjust manually)
          console.log('切换到全球模式 (Switched to global mode)')
        }
      } catch (error) {
        console.error('更新地图视图失败 (Failed to update map view):', error)
      }
    },

    // 公共方法用于外部控制 (Public methods for external control)
    addPath(pathData) {
      const newPaths = Array.isArray(pathData) ? pathData : [pathData]
      this.$emit('update:pathData', [...this.pathData, ...newPaths])
    },

    removePath(index) {
      if (index >= 0 && index < this.pathData.length) {
        const newPaths = [...this.pathData]
        newPaths.splice(index, 1)
        this.$emit('update:pathData', newPaths)
      }
    },

    clearPaths() {
      this.$emit('update:pathData', [])
    },



    /**
     * 设置区域模式 (Set regional mode)
     * @param {Object} regionConfig - 区域配置对象 (Regional configuration object)
     */
    setRegionalMode(regionConfig) {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', true)
      this.$emit('update:regionConfig', { ...this.regionConfig, ...regionConfig })
      this.updateMapView()
    },

    /**
     * 切换到全球模式 (Switch to global mode)
     */
    setGlobalMode() {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', false)
      this.updateMapView()
    }
  }
}
</script>

<style scoped>
.standalone-world-map {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  border-radius: 8px;
  overflow: hidden;
}

.map-title {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.title-text {
  color: #00FFCC;
  font-size: 16px;
  font-weight: 500;
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
}

/* Mapbox GL JS 样式覆盖 (Mapbox GL JS style overrides) */
.map-container :deep(.mapboxgl-canvas-container) {
  background-color: #0F2E2C !important;
}

.map-container :deep(.mapboxgl-canvas) {
  background-color: #0F2E2C !important;
}

/* 隐藏Mapbox标志和属性 (Hide Mapbox logo and attribution) */
.map-container :deep(.mapboxgl-ctrl-logo) {
  display: none !important;
}

.map-container :deep(.mapboxgl-ctrl-attrib) {
  display: none !important;
}

/* 标记样式 (Marker styles) */
.map-container :deep(.marker) {
  transition: all 0.3s ease;
}

.map-container :deep(.marker:hover) {
  transform: scale(1.2);
}

/* 流动粒子样式 (Flow particle styles) */
.map-container :deep(.flow-particle) {
  transition: opacity 0.1s ease;
}

/* 网格覆盖层样式 - 仅在启用时显示 (Grid overlay styles - only show when enabled) */
.map-container.show-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 10;
}

/* 发光效果 (Glow effect) */
.map-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
  pointer-events: none;
  z-index: 11;
}
</style>
